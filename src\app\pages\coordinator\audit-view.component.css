body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
}
.month-view {
    display: none;
}

.width-300 {
    width: 300px;
}

.min-width-120 {
    min-width: 120px;
}


.wintable td,
.wintable th {
  padding: .5rem;
  min-width: 130px;
}

.font-small {
    font-size: small;
}

.font-smaller {
    font-size: smaller;
}

.font-medium {
    font-size: medium;
}

.font-x-large {
    font-size: x-large;
}

.font-xxx-large {
    font-size: xxx-large;
}

.custom-date-range {
    width: 100%;
    min-height: calc(1.5em + .75rem + 5px);
    padding: .375rem .375rem;
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.att-info-box {
    position: relative;
    padding-bottom: 15px !important;
}

.info-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    margin: 5px;
    background-color: #fff;
    border-radius: 50%;
}
.day-of-month{
    float: right;
    font-size: 1rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
}

.monthly-popup .table td {
    vertical-align: top !important;
    width: 133px;
    min-height: 113px;
}

.monthly-popup .att-info-box {
  position: unset;
  margin-top: 20px !important;
  display: inline-block;
  min-width: 100%;
  min-height: 90%;
}
.monthly-popup  .date-box{
    width: 100%;
}
.monthly-popup .date-label{
    right: 0px;
    top: 0px;
}
.outer {
    position: relative;
    max-height: 65vh;
  }

  .outer .inner {
    overflow-x: auto;
    overflow-y: auto;
    max-height: inherit;
    margin-left: 20vw;
  }

  .outer .inner .wintable {
    width: 100% !important;
    border-collapse: separate;
    border-spacing: 0;
    table-layout: fixed;
  }

  .outer .inner .wintable thead th {
    position: sticky;
    top: 0;
    z-index: 2;
    background: #0169ab !important;
    text-align: center;
    color: white;
    padding: 1rem;
    border: 1px solid #dee2e6;
  }

  .outer .inner .wintable td {
    border: 1px solid #dee2e6;
    vertical-align: top;
    background: white;
  }

  /* Fixed left column styling */
  .outer .inner .fix {
    position: absolute !important;
    width: 20vw;
    left: 0;
    margin-left: -20vw;
    background: white;
    border-right: 1px solid #dee2e6;
    z-index: 3;
  }

  .wintable .fix {
    min-height: 93px;
    padding: 0.5rem;
  }

  /* Patient info in fixed column */
  .fix .row {
    margin: 0 -5px;
  }
  
  .fix .col-6 {
    padding: 0 5px;
  }

  .day-body-col.fix {
    border: 1px solid #dee2e6 !important;
    background: white;
  }

  .cell-min-height {
    min-height: 96px;
  }


@media (min-width: 577px) {
    .container {
        width: 100%;
        max-width: unset;
        padding: 0 30px;
    }
    .outer {
        margin-left: 20vw;
        position: relative;
        background-color: #fff;
      }
}

@media (max-width: 576px) {
    .min-width-80 {
        min-width: 80px;
    }

    .filter-icon-wrapper {
        fill: white;
    }

    .container {
        padding: 0;
    }

    .carousel-inner {
        position: relative;
        overflow: hidden;
    }

    .carousel-item {
        width: 90%;
        margin: auto;
        text-align: center;
        float: none;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 5%;
        opacity: 1;
        background: transparent;
    }

    .carousel-control-prev-icon {
        background-image: url(../../../assets/left-arrow-next.svg);
    }

    .carousel-control-next-icon {
        background-image: url(../../../assets/right-arrow-next.svg);
    }

    #myCarousel {
        overflow: hidden;
    }

    .card-head,
    .card-body {
        border: 1px solid #fff;
    }

    .close-button {
        float: right;
        margin-top: 0px;
        margin-right: 10px;
    }

    .close-button img {
        width: 15px;
    }  
    
}
